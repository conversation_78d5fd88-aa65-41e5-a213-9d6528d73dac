<?php

namespace Tests\Unit\Console\Commands;

use App\Console\Commands\CheckSaleDistributionStatusCommand;
use App\Sale;
use App\Services\Enums\Ceiling;
use App\Services\Sales\Ceiling\DistributionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class CheckSaleDistributionStatusCommandTest extends TestCase
{
    use RefreshDatabase;

    private $distributionService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->distributionService = Mockery::mock(DistributionService::class);
        $this->app->instance(DistributionService::class, $this->distributionService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_shows_error_for_invalid_sale_id()
    {
        $this->artisan('distribution:check-sale-status', ['sale_id' => 'abc'])
            ->expectsOutput('Sale ID must be a positive integer')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_shows_error_for_non_existent_sale()
    {
        $this->artisan('distribution:check-sale-status', ['sale_id' => 99999])
            ->expectsOutput('Sale with ID 99999 not found')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_correctly_identifies_below_ceiling_sale()
    {
        // Create a sale with BELOW ceiling
        $sale = Sale::factory()->create([
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Mock the distribution service to return empty results
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andReturn(collect([]));

        $this->artisan('distribution:check-sale-status', ['sale_id' => $sale->id])
            ->expectsOutput('Distributed: ❌ NO')
            ->expectsOutput('Ceiling Status: BELOW (0)')
            ->expectsOutput('📋 This sale HAS NOT BEEN DISTRIBUTED')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_correctly_identifies_distributed_sale()
    {
        // Create a sale with DISTRIBUTED ceiling
        $sale = Sale::factory()->create([
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'quantity' => 150,
            'value' => 3000,
            'bonus' => 25,
            'sale_ids' => '1,2,3'
        ]);

        $this->artisan('distribution:check-sale-status', ['sale_id' => $sale->id])
            ->expectsOutput('Distributed: ✅ YES')
            ->expectsOutput('Ceiling Status: DISTRIBUTED (2)')
            ->expectsOutput('🎯 This sale HAS BEEN DISTRIBUTED')
            ->expectsOutput('→ This is a distributed sale created from ceiling violation')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_correctly_identifies_above_ceiling_sale()
    {
        // Create a sale with ABOVE ceiling
        $sale = Sale::factory()->create([
            'ceiling' => Ceiling::ABOVE->value,
            'quantity' => 200,
            'value' => 4000,
            'bonus' => 30
        ]);

        // Create a related distributed sale
        $distributedSale = Sale::factory()->create([
            'ceiling' => Ceiling::DISTRIBUTED->value,
            'product_id' => $sale->product_id,
            'distributor_id' => $sale->distributor_id,
            'date' => $sale->date,
            'sale_ids' => $sale->id . ',999',
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->artisan('distribution:check-sale-status', ['sale_id' => $sale->id])
            ->expectsOutput('Distributed: ✅ YES')
            ->expectsOutput('Ceiling Status: ABOVE (1)')
            ->expectsOutput('🎯 This sale HAS BEEN DISTRIBUTED')
            ->expectsOutput('→ This is an original sale that was distributed (marked as ABOVE)')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_shows_detailed_analysis_when_requested()
    {
        $sale = Sale::factory()->create([
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        // Mock the distribution service
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andReturn(collect([]));

        $this->artisan('distribution:check-sale-status', [
                'sale_id' => $sale->id,
                '--detailed' => true
            ])
            ->expectsOutput('=== DETAILED ANALYSIS ===')
            ->expectsOutput('--- Mapping Details ---')
            ->expectsOutput('--- Sales Details Breakdown ---')
            ->expectsOutput('--- Distribution Service Query Simulation ---')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_validates_distribution_eligibility_criteria()
    {
        $sale = Sale::factory()->create([
            'ceiling' => Ceiling::BELOW->value,
            'quantity' => 50,
            'value' => 1000,
            'bonus' => 10
        ]);

        $this->artisan('distribution:check-sale-status', ['sale_id' => $sale->id])
            ->expectsOutput('=== DISTRIBUTION ELIGIBILITY ANALYSIS ===')
            ->expectsOutput('Criteria Checks:')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_handles_command_execution_errors_gracefully()
    {
        // Create a sale that might cause issues
        $sale = Sale::factory()->create();

        // Mock the distribution service to throw an exception
        $this->distributionService
            ->shouldReceive('queryCeilingSales')
            ->andThrow(new \Exception('Database connection error'));

        $this->artisan('distribution:check-sale-status', [
                'sale_id' => $sale->id,
                '--detailed' => true
            ])
            ->expectsOutput('Error simulating distribution query: Database connection error')
            ->assertExitCode(0);
    }
}
