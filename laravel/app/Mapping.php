<?php

namespace App;

use App\Models\MappingSale;
use App\Models\SaleFactor;
use App\Models\SaleFactorType;
use App\Models\UnifiedPharmacyType;
use App\Traits\ModelImportable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Staudenmeir\EloquentHasManyDeep\HasManyDeep;
use Staudenmeir\EloquentHasManyDeep\HasRelationships;

class Mapping extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use HasRelationships;

    // use ModelNormalImportable;
    protected $guard_name = 'api';

    protected $table = 'mappings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        "id",
        'code',
        'uc_code',
        'address',
        'pharmacy_type_id',
        'unified_pharmacy_type_id',
        'name',
        'distributor_id',
        'line_id',
        'mapping_type_id',
        'from_date',
        'to_date',
        'file_id',
        'exception'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
        'exception' => 'boolean'
    ];


    public function unifiedPharmacyType(): BelongsTo
    {
        return $this->belongsTo(UnifiedPharmacyType::class);
    }

    public function unifiedCode()
    {
        return $this->hasOne(MappingUnifiedCode::class, 'code', 'uc_code');
    }

    public function details()
    {
        return $this->hasMany(MappingDetail::class);
    }

    public function bricks(): HasManyThrough
    {
        return $this->hasManyThrough(Brick::class, MappingDetail::class, 'mapping_id', 'id', 'id', 'brick_id');
    }

    public function lineBricks(): HasManyDeep
    {
        return $this->hasManyDeepFromRelations($this->bricks(), (new Brick())->lineBricks());
    }

    public function sales(): BelongsToMany
    {
        return $this->belongsToMany(Sale::class)->using(MappingSale::class);
    }

    public function mappingSale(): HasMany
    {
        return $this->hasMany(MappingSale::class);
    }

    public function salesDetails(): HasManyThrough
    {
        return $this->hasManyDeepFromRelations($this->sales(), (new Sale())->details());
    }

    public function distributor()
    {
        return $this->belongsTo(Distributor::class);
    }

    public function type()
    {
        return $this->belongsTo(SalesTypes::class, 'mapping_type_id', 'id');
    }

    public function line()
    {
        return $this->belongsTo(Line::class);
    }

    public function saleFactors(): BelongsToMany
    {
        return $this->belongsToMany(SaleFactor::class, "sale_factor_types")
            ->using(SaleFactorType::class)
            ->where('sale_factor_types.from_date', '<=', now())
            ->where(fn($q) => $q->where('sale_factor_types.to_date', '>', (string)Carbon::now())
                ->orWhere('sale_factor_types.to_date', null));
    }

    public function saleFactorTypes(): HasMany
    {
        return $this->HasMany(SaleFactorType::class)
            ->where('sale_factor_types.from_date', '<=', now())
            ->where(fn($q) => $q->where('sale_factor_types.to_date', '>', (string)Carbon::now())
                ->orWhere('sale_factor_types.to_date', null));
    }

    public function updateFactor($factor_id)
    {
        $old_factor = $this->saleFactorTypes->first();

        if ($old_factor) {
            $old_factor->to_date = now();
            $old_factor->save();
        }

        SaleFactorType::firstOrCreate([
            "mapping_id" => $this->id,
            "sale_factor_id" => $factor_id,
            "from_date" => now(),
            "to_date" => null,
        ]);
    }

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }


    public function restore()
    {
        $this->withTrashed()->where('id', $this->id)->restore();
        MappingDetail::withTrashed()->where('mapping_id', $this->id)->restore();
    }


    public function forceDelete()
    {
        MappingDetail::withTrashed()->where('mapping_id', $this->id)->forceDelete();
        SaleFactorType::where('mapping_id', $this->id)->delete();
        $this->withTrashed()->where('id', $this->id)->forceDelete();
    }
}
