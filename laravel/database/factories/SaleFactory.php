<?php

namespace Database\Factories;

use App\Sale;
use App\Product;
use App\Distributor;
use App\Services\Enums\Ceiling;
use Illuminate\Database\Eloquent\Factories\Factory;

class SaleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sale::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'product_id' => Product::inRandomOrder()->first()?->id ?? 1,
            'distributor_id' => Distributor::inRandomOrder()->first()?->id ?? 1,
            'quantity' => $this->faker->randomFloat(5, 1, 1000),
            'value' => $this->faker->randomFloat(5, 0, 10000),
            'bonus' => $this->faker->randomFloat(1, 0, 100),
            'region' => $this->faker->numberBetween(1, 10),
            'date' => $this->faker->date(),
            'ceiling' => Ceiling::BELOW->value,
            'sale_ids' => null,
        ];
    }

    /**
     * Indicate that the sale has BELOW ceiling status.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function below()
    {
        return $this->state(function (array $attributes) {
            return [
                'ceiling' => Ceiling::BELOW->value,
            ];
        });
    }

    /**
     * Indicate that the sale has ABOVE ceiling status.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function above()
    {
        return $this->state(function (array $attributes) {
            return [
                'ceiling' => Ceiling::ABOVE->value,
            ];
        });
    }

    /**
     * Indicate that the sale has DISTRIBUTED ceiling status.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function distributed()
    {
        return $this->state(function (array $attributes) {
            return [
                'ceiling' => Ceiling::DISTRIBUTED->value,
                'sale_ids' => $this->faker->randomNumber() . ',' . $this->faker->randomNumber(),
            ];
        });
    }

    /**
     * Indicate that the sale exceeds ceiling limits.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function exceedsCeiling()
    {
        return $this->state(function (array $attributes) {
            return [
                'quantity' => $this->faker->randomFloat(5, 1000, 5000), // Large quantity
            ];
        });
    }
}
